# Hướng dẫn tạo Logo để che Watermark

## 🎨 **Option 1: Sử dụng Logo có sẵn**

### **Nếu bạn đã có logo:**
1. **Convert sang PNG** (nếu cần):
   - Online: [convertio.co](https://convertio.co)
   - Photoshop: Export as PNG với transparent background
   - GIMP: Export as PNG

2. **Resize logo** (nếu cần):
   - Recommended size: 200x60 pixels
   - Maintain aspect ratio
   - High quality/resolution

3. **Đặt vào public folder:**
   ```bash
   cp your-logo.png public/logo.png
   ```

## 🛠️ **Option 2: Tạo Logo đơn giản**

### **Sử dụng sample SVG:**
1. **Edit file `sample-logo.svg`:**
   ```xml
   <!-- Thay đổi text -->
   <text x="100" y="25">YOUR BRAND NAME</text>
   <text x="100" y="45">Your Tagline</text>
   
   <!-- Thay đổi màu -->
   <rect fill="rgba(255, 255, 255, 0.9)"/>  <!-- Background -->
   <text fill="#333">                        <!-- Text color -->
   <circle fill="#007bff"/>                  <!-- Accent color -->
   ```

2. **Convert SVG sang PNG:**
   
   **Online Tools:**
   - [svgtopng.com](https://svgtopng.com)
   - [cloudconvert.com](https://cloudconvert.com)
   - [convertio.co](https://convertio.co)
   
   **Command Line (nếu có ImageMagick):**
   ```bash
   convert sample-logo.svg -background transparent public/logo.png
   ```
   
   **Browser Method:**
   - Mở SVG trong browser
   - Screenshot với transparent background
   - Crop và save as PNG

## 🎯 **Option 3: Logo chuyên nghiệp**

### **Design Tools:**
- **Canva**: Templates có sẵn, dễ sử dụng
- **Figma**: Professional design tool
- **Adobe Illustrator**: Advanced vector design
- **LogoMaker**: AI-powered logo generation

### **Logo Requirements cho che Watermark:**
- **Background**: Semi-transparent hoặc solid
- **Size**: 120-200px width, 30-60px height
- **Colors**: Contrast tốt với video background
- **Style**: Simple, clean, readable

## 📐 **Sizing Guide**

### **Watermark Hailuo AI thường:**
- Size: ~100x30 pixels
- Position: Bottom-right, 15-20px from edges

### **Logo để che watermark nên:**
- Size: 120-150px width, 35-50px height
- Lớn hơn watermark 20-30%
- Có background để che hoàn toàn

## 🎨 **Design Tips**

### **Colors:**
```css
/* Light background videos */
background: rgba(255, 255, 255, 0.8)  /* White semi-transparent */
text: #333                            /* Dark text */

/* Dark background videos */  
background: rgba(0, 0, 0, 0.7)        /* Black semi-transparent */
text: #fff                            /* White text */

/* Universal (recommended) */
background: rgba(255, 255, 255, 0.9)  /* White with high opacity */
text: #333                            /* Dark text */
border: rgba(0, 0, 0, 0.1)            /* Subtle border */
```

### **Typography:**
- **Font**: Sans-serif (Arial, Helvetica, Roboto)
- **Weight**: Bold hoặc Semi-bold
- **Size**: 14-20px cho main text, 10-14px cho subtitle

### **Layout:**
```
┌─────────────────────────┐
│  ●  YOUR BRAND NAME  ●  │  ← Main text
│     Professional Videos │  ← Subtitle (optional)
└─────────────────────────┘
```

## 🔧 **Quick Templates**

### **Template 1: Text Only**
```xml
<svg width="180" height="40">
  <rect width="180" height="40" rx="6" fill="rgba(255,255,255,0.9)"/>
  <text x="90" y="28" font-family="Arial" font-size="16" font-weight="bold" 
        text-anchor="middle" fill="#333">YOUR BRAND</text>
</svg>
```

### **Template 2: With Icon**
```xml
<svg width="200" height="50">
  <rect width="200" height="50" rx="8" fill="rgba(255,255,255,0.9)"/>
  <circle cx="25" cy="25" r="10" fill="#007bff"/>
  <text x="50" y="32" font-family="Arial" font-size="18" font-weight="bold" fill="#333">
    YOUR BRAND
  </text>
</svg>
```

### **Template 3: Minimal**
```xml
<svg width="150" height="35">
  <rect width="150" height="35" rx="4" fill="rgba(0,0,0,0.8)"/>
  <text x="75" y="24" font-family="Arial" font-size="14" font-weight="bold" 
        text-anchor="middle" fill="white">YOUR BRAND</text>
</svg>
```

## ✅ **Testing**

### **After creating logo:**
1. **Place in public/logo.png**
2. **Run `npm run dev`**
3. **Check in Remotion Studio:**
   - Logo position correct?
   - Covers watermark completely?
   - Readable and professional?
   - Consistent across all videos?

### **Fine-tuning:**
- Adjust `size` in config
- Modify `offset` for perfect positioning  
- Change `opacity` for better blend
- Update `backgroundColor` for better coverage

**Tạo logo professional để che watermark Hailuo AI!** 🎨
