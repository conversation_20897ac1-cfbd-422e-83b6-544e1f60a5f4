# Quick Setup: Logo để che Watermark Hailuo AI

## 🚀 **Setup nhanh trong 3 bước**

### **Bước 1: Thêm Logo File**
```bash
# Đặt file logo.png vào public/ folder
cp your-logo.png public/logo.png
```

### **Bước 2: Ki<PERSON><PERSON> tra Config** 
Logo đã được enable sẵn trong `src/config/shortVideoConfig.ts`:
```typescript
logo: {
  enabled: true,                      // ✅ Đã enable
  src: 'logo.png',                   // ✅ Trỏ đến public/logo.png
  position: 'bottom-right',          // ✅ Che watermark Hailuo AI
  size: { width: 120, height: 40 }, // ✅ Size phù hợp
  offset: { x: -20, y: -20 },       // ✅ Fine-tune position
  opacity: 0.9,                     // ✅ Độ trong suốt tốt
  style: {
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.1)', // ✅ Background che watermark
    padding: 5,
  }
}
```

### **Bước 3: Test**
```bash
npm run dev
```

## ⚙️ **Điều chỉnh nhanh**

### **Nếu logo quá nhỏ:**
```typescript
size: { width: 150, height: 50 }
```

### **Nếu logo không che hết watermark:**
```typescript
offset: { x: -30, y: -30 }  // Dịch chuyển nhiều hơn
style: {
  backgroundColor: 'rgba(255, 255, 255, 0.3)', // Background đục hơn
  padding: 8,
}
```

### **Nếu logo quá nổi bật:**
```typescript
opacity: 0.7  // Giảm opacity
```

### **Disable logo:**
```typescript
enabled: false
```

## 📋 **Logo Requirements**

- **Format**: PNG (với transparent background) hoặc JPG
- **Size**: Tối thiểu 200x60 pixels
- **File name**: `logo.png` (hoặc update `src` trong config)
- **Location**: `public/logo.png`

## ✅ **Expected Result**

- ✅ Logo xuất hiện ở góc bottom-right
- ✅ Che được watermark "Hailuo AI" 
- ✅ Logo hiển thị trên tất cả videos
- ✅ Không che nội dung video quan trọng
- ✅ Styling professional và consistent

**Done! Logo sẽ che watermark Hailuo AI trong tất cả videos.** 🎉
