# Schema Support cho ShortVideo Composition

## ✅ **Tính năng mới: Editable Video Titles**

Đã thêm schema support cho ShortVideo composition, cho phép edit video titles và các settings khác trực tiếp trong Remotion Studio.

## 🎯 **Features**

### **1. Custom Video Titles:**
- **Default values**: Tên file video (không extension)
- **Editable**: <PERSON><PERSON> thể edit từng video title riêng biệt
- **Dynamic**: Tự động tạo fields dựa trên số lượng videos

### **2. UI Controls:**
- **Show/Hide toggles**: Video counter, progress bar, logo, titles
- **Title styling**: Font size, weight, color, background
- **Real-time preview**: Thay đổi ngay lập tức trong Remotion Studio

## 🔧 **Cách sử dụng**

### **1. Mở Remotion Studio:**
```bash
npm run dev
```

### **2. Chọn ShortVideo Composition:**
- Trong Remotion Studio, chọn "ShortVideo" composition
- Bên phải sẽ xuất hiện **Props Panel** với các options

### **3. Edit Video Titles:**
```
Video Titles:
├── video_0: "sabo"           ← Edit title cho video đầu tiên
├── video_1: "another-video"  ← Edit title cho video thứ hai  
└── video_2: "final-clip"     ← Edit title cho video thứ ba
```

### **4. Customize Settings:**
```
Global Settings:
├── Show Video Titles: ✅     ← Bật/tắt hiển thị titles
├── Show Video Counter: ✅    ← Bật/tắt counter (1/3, 2/3...)
├── Show Progress Bar: ✅     ← Bật/tắt progress bar
└── Show Logo: ✅            ← Bật/tắt logo overlay

Title Style:
├── Font Size: 32px          ← Kích thước chữ
├── Font Weight: bold        ← Độ đậm chữ
├── Color: #ffffff           ← Màu chữ
├── Background: rgba(0,0,0,0.7) ← Màu nền
└── Border Radius: 10px      ← Bo góc
```

## 📋 **Schema Structure**

### **Video Titles (Dynamic):**
```typescript
// Tự động tạo fields dựa trên videos có sẵn
videoTitles: {
  video_0: "sabo",              // Default: filename without extension
  video_1: "another-video",     // Default: filename without extension
  video_2: "final-clip",        // Default: filename without extension
}
```

### **Global Settings:**
```typescript
showVideoTitles: true,    // Hiển thị titles
showVideoCounter: true,   // Hiển thị counter (1/3, 2/3...)
showProgressBar: true,    // Hiển thị progress bar
showLogo: true,          // Hiển thị logo overlay
```

### **Title Styling:**
```typescript
titleStyle: {
  fontSize: 32,                           // 16-60px
  fontWeight: 'bold',                     // normal, bold, bolder
  color: '#ffffff',                       // Hex color
  backgroundColor: 'rgba(0, 0, 0, 0.7)', // RGBA color
  borderRadius: 10,                       // 0-20px
}
```

## 🎨 **Customization Examples**

### **Example 1: Minimal Style**
```typescript
// Trong Remotion Studio Props Panel
titleStyle: {
  fontSize: 24,
  fontWeight: 'normal',
  color: '#ffffff',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  borderRadius: 5,
}
```

### **Example 2: Bold & Colorful**
```typescript
titleStyle: {
  fontSize: 40,
  fontWeight: 'bolder',
  color: '#ffff00',  // Yellow text
  backgroundColor: 'rgba(255, 0, 0, 0.8)', // Red background
  borderRadius: 15,
}
```

### **Example 3: Clean & Professional**
```typescript
titleStyle: {
  fontSize: 28,
  fontWeight: 'bold',
  color: '#333333',  // Dark text
  backgroundColor: 'rgba(255, 255, 255, 0.9)', // White background
  borderRadius: 8,
}
```

## 🔄 **Workflow**

### **1. Setup Videos:**
```bash
# Thêm videos vào public/videos/
public/videos/
├── intro-scene.mp4
├── main-content.mp4
└── outro-credits.mp4

# Generate video list
npm run prepare:assets
```

### **2. Open Remotion Studio:**
```bash
npm run dev
```

### **3. Customize in UI:**
- **Select ShortVideo composition**
- **Edit video titles** trong Props Panel:
  - `video_0`: "Introduction" 
  - `video_1`: "Main Content"
  - `video_2`: "Credits"
- **Adjust styling** theo ý muốn
- **Toggle overlays** on/off

### **4. Preview & Render:**
- **Real-time preview** trong Remotion Studio
- **Render final video**:
  ```bash
  npx remotion render ShortVideo out/custom-short-video.mp4
  ```

## 📊 **Benefits**

### **Before (No Schema):**
- ❌ Video titles = filenames (fixed)
- ❌ Phải edit code để thay đổi titles
- ❌ Không có UI controls
- ❌ Khó customize cho non-developers

### **After (With Schema):**
- ✅ **Editable video titles** trong UI
- ✅ **Real-time preview** của changes
- ✅ **No code editing** required
- ✅ **User-friendly** cho non-developers
- ✅ **Professional customization** options

## 🎯 **Use Cases**

### **1. Content Creator:**
```
Videos: "raw-footage-1.mp4", "raw-footage-2.mp4"
Custom Titles: "Epic Intro", "Amazing Finale"
```

### **2. Brand Marketing:**
```
Videos: "product-demo.mp4", "testimonial.mp4"  
Custom Titles: "Product Features", "Customer Reviews"
```

### **3. Educational Content:**
```
Videos: "lesson-1.mp4", "lesson-2.mp4"
Custom Titles: "Introduction to React", "Advanced Concepts"
```

## 🔧 **Advanced Usage**

### **Programmatic Rendering:**
```bash
# Render với custom props
npx remotion render ShortVideo out/video.mp4 --props='{"videoTitles":{"video_0":"Custom Title 1","video_1":"Custom Title 2"}}'
```

### **Batch Rendering:**
```typescript
// Script để render nhiều versions
const variations = [
  { videoTitles: { video_0: "Version A", video_1: "Content A" } },
  { videoTitles: { video_0: "Version B", video_1: "Content B" } },
];

// Render each variation...
```

## ✅ **Verification**

### **Test Checklist:**
- ✅ Props Panel xuất hiện trong Remotion Studio
- ✅ Video titles có default values (filenames)
- ✅ Edit titles → preview updates real-time
- ✅ Toggle overlays on/off works
- ✅ Title styling changes apply immediately
- ✅ Render với custom props works

**Schema support hoàn toàn functional!** 🎉
