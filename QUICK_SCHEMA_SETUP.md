# Quick Start: Edit Video Titles với Schema

## 🚀 **3 bước để edit video titles**

### **Bước 1: Install dependencies**
```bash
npm install
```

### **Bước 2: Start Remotion Studio**
```bash
npm run dev
```

### **Bước 3: Edit trong UI**
1. **Chọn "ShortVideo" composition**
2. **Mở Props Panel** (bên phải)
3. **Edit video titles:**
   ```
   Video Titles:
   ├── video_0: "Your Custom Title 1"  ← Edit here
   ├── video_1: "Your Custom Title 2"  ← Edit here
   └── video_2: "Your Custom Title 3"  ← Edit here
   ```

## ⚡ **Available Controls**

### **Video Titles:**
- **Default**: Tên file (không extension)
- **Editable**: Click để edit từng title

### **Show/Hide Options:**
- ✅ **Show Video Titles** - Bật/tắt hiển thị titles
- ✅ **Show Video Counter** - Bật/tắt counter (1/3, 2/3...)  
- ✅ **Show Progress Bar** - Bật/tắt progress bar
- ✅ **Show Logo** - Bật/tắt logo overlay

### **Title Styling:**
- **Font Size**: 16-60px (default: 32px)
- **Font Weight**: normal, bold, bolder
- **Color**: Hex color picker
- **Background**: RGBA color với transparency
- **Border Radius**: 0-20px bo góc

## 🎯 **Quick Examples**

### **Example 1: Simple Edit**
```
video_0: "Introduction"
video_1: "Main Content"  
video_2: "Conclusion"
```

### **Example 2: Brand Names**
```
video_0: "Product Demo"
video_1: "Customer Reviews"
video_2: "Call to Action"
```

### **Example 3: Educational**
```
video_0: "Lesson 1: Basics"
video_1: "Lesson 2: Advanced"
video_2: "Summary & Quiz"
```

## ✅ **Expected Result**

- ✅ **Real-time preview** của title changes
- ✅ **Professional styling** với custom fonts/colors
- ✅ **No code editing** required
- ✅ **Easy customization** cho non-developers

**Done! Video titles bây giờ có thể edit trực tiếp trong Remotion Studio UI.** 🎉
