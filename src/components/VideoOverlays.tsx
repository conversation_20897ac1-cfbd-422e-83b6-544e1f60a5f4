import React from 'react';
import { Img, staticFile } from 'remotion';

interface VideoCounterProps {
  current: number;
  total: number;
  style?: React.CSSProperties;
}

export const VideoCounter: React.FC<VideoCounterProps> = ({
  current,
  total,
  style = {}
}) => (
  <div
    style={{
      position: 'absolute',
      top: 40,
      right: 40,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      color: 'white',
      padding: '10px 20px',
      borderRadius: 20,
      fontSize: 24,
      fontWeight: 'bold',
      zIndex: 10,
      ...style
    }}
  >
    {current} / {total}
  </div>
);

interface VideoTitleProps {
  title: string;
  style?: React.CSSProperties;
}

export const VideoTitle: React.FC<VideoTitleProps> = ({
  title,
  style = {}
}) => (
  <div
    style={{
      position: 'absolute',
      bottom: 100,
      left: 40,
      right: 40,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      color: 'white',
      padding: '20px',
      borderRadius: 10,
      fontSize: 32,
      fontWeight: 'bold',
      textAlign: 'center',
      zIndex: 10,
      ...style
    }}
  >
    {title}
  </div>
);

interface ProgressBarProps {
  progress: number; // 0 to 1
  style?: React.CSSProperties;
  barColor?: string;
  backgroundColor?: string;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  style = {},
  barColor = '#ff4444',
  backgroundColor = 'rgba(255, 255, 255, 0.3)'
}) => (
  <div
    style={{
      position: 'absolute',
      bottom: 40,
      left: 40,
      right: 40,
      height: 6,
      backgroundColor,
      borderRadius: 3,
      zIndex: 10,
      ...style
    }}
  >
    <div
      style={{
        width: `${Math.max(0, Math.min(100, progress * 100))}%`,
        height: '100%',
        backgroundColor: barColor,
        borderRadius: 3,
        transition: 'width 0.1s ease-out',
      }}
    />
  </div>
);

interface WatermarkProps {
  text: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  style?: React.CSSProperties;
}

export const Watermark: React.FC<WatermarkProps> = ({
  text,
  position = 'bottom-right',
  style = {}
}) => {
  const getPositionStyle = (): React.CSSProperties => {
    switch (position) {
      case 'top-left':
        return { top: 20, left: 20 };
      case 'top-right':
        return { top: 20, right: 20 };
      case 'bottom-left':
        return { bottom: 20, left: 20 };
      case 'bottom-right':
      default:
        return { bottom: 20, right: 20 };
    }
  };

  return (
    <div
      style={{
        position: 'absolute',
        ...getPositionStyle(),
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        color: 'white',
        padding: '5px 10px',
        borderRadius: 5,
        fontSize: 16,
        fontWeight: 'normal',
        zIndex: 5,
        opacity: 0.8,
        ...style
      }}
    >
      {text}
    </div>
  );
};

interface LogoProps {
  src: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  size?: { width: number; height: number };
  offset?: { x: number; y: number };
  opacity?: number;
  style?: React.CSSProperties;
}

export const Logo: React.FC<LogoProps> = ({
  src,
  position = 'bottom-right',
  size = { width: 120, height: 40 },
  offset = { x: 0, y: 0 },
  opacity = 1,
  style = {}
}) => {
  const getPositionStyle = (): React.CSSProperties => {
    const baseOffset = 20; // Default offset from edges

    switch (position) {
      case 'top-left':
        return {
          top: baseOffset + offset.y,
          left: baseOffset + offset.x
        };
      case 'top-right':
        return {
          top: baseOffset + offset.y,
          right: baseOffset - offset.x
        };
      case 'bottom-left':
        return {
          bottom: baseOffset - offset.y,
          left: baseOffset + offset.x
        };
      case 'bottom-right':
      default:
        return {
          bottom: baseOffset - offset.y,
          right: baseOffset - offset.x
        };
    }
  };

  return (
    <div
      style={{
        position: 'absolute',
        ...getPositionStyle(),
        width: size.width,
        height: size.height,
        zIndex: 15, // Higher than other overlays to cover watermarks
        opacity,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...style
      }}
    >
      <Img
        src={staticFile(src)}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
        }}
      />
    </div>
  );
};
