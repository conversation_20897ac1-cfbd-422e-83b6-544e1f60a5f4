import { Video, staticFile, interpolate } from 'remotion';

interface VideoTransitionProps {
  currentVideo: {
    filename: string;
    durationInFrames: number;
    durationInSeconds: number;
  };
  nextVideo?: {
    filename: string;
    durationInFrames: number;
    durationInSeconds: number;
  };
  frameInCurrentVideo: number;
  transitionDuration: number;
  style?: React.CSSProperties;
}

export const VideoTransition: React.FC<VideoTransitionProps> = ({
  currentVideo,
  nextVideo,
  frameInCurrentVideo,
  transitionDuration,
  style = {}
}) => {
  const isInTransition = frameInCurrentVideo >= currentVideo.durationInFrames - transitionDuration;
  
  const currentOpacity = isInTransition 
    ? interpolate(
        frameInCurrentVideo,
        [currentVideo.durationInFrames - transitionDuration, currentVideo.durationInFrames],
        [1, 0],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      )
    : 1;

  const nextOpacity = isInTransition 
    ? interpolate(
        frameInCurrentVideo,
        [currentVideo.durationInFrames - transitionDuration, currentVideo.durationInFrames],
        [0, 1],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      )
    : 0;

  const baseVideoStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    ...style
  };

  return (
    <>
      {/* Current video */}
      <Video
        src={staticFile(`videos/${currentVideo.filename}`)}
        startFrom={0}
        endAt={currentVideo.durationInFrames}
        style={{
          ...baseVideoStyle,
          opacity: currentOpacity,
        }}
      />

      {/* Next video for transition (only show during transition) */}
      {isInTransition && nextVideo && (
        <Video
          src={staticFile(`videos/${nextVideo.filename}`)}
          startFrom={0}
          endAt={nextVideo.durationInFrames}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            ...baseVideoStyle,
            opacity: nextOpacity,
          }}
        />
      )}
    </>
  );
};
