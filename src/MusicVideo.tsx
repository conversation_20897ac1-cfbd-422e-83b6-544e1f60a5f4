import {
  useCurrentFrame,
  Audio,
  staticFile,
  Img,
  AbsoluteFill,
  interpolate,
} from 'remotion';
import { images } from './image-list';

const FPS = 30;
const DURATION_PER_IMAGE_IN_FRAMES = 5 * FPS; // 5 seconds per image
const TRANSITION_DURATION = 15; // dissolve duration in frames (~0.5s)

export const MusicVideo = () => {
  const frame = useCurrentFrame();

  // Current image index
  const currentIndex = Math.floor(frame / DURATION_PER_IMAGE_IN_FRAMES) % images.length;
  // Next image index (loop back to 0 if at end)
  const nextIndex = (currentIndex + 1) % images.length;

  // Frame inside current image duration
  const frameInImage = frame % DURATION_PER_IMAGE_IN_FRAMES;

  // Opacity of current image: stays 1 then fades out in transition duration
  const currentOpacity = interpolate(
    frameInImage,
    [DURATION_PER_IMAGE_IN_FRAMES - TRANSITION_DURATION, DURATION_PER_IMAGE_IN_FRAMES],
    [1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Opacity of next image: fades in during transition duration
  const nextOpacity = interpolate(
    frameInImage,
    [DURATION_PER_IMAGE_IN_FRAMES - TRANSITION_DURATION, DURATION_PER_IMAGE_IN_FRAMES],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // Music bars fixed style
  const NUM_BARS = 20;
  const barMaxHeight = 40;

  return (
    <div style={{ flex: 1, backgroundColor: 'black' }}>
      <Audio src={staticFile('music/song.mp3')} />

      <AbsoluteFill>
        {/* Current image fading out */}
        <Img
          src={staticFile(`images/${images[currentIndex]}`)}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 0,
            opacity: currentOpacity,
          }}
        />

        {/* Next image fading in */}
        <Img
          src={staticFile(`images/${images[nextIndex]}`)}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            zIndex: 1,
            opacity: nextOpacity,
          }}
        />

        {/* Fixed title in center top */}
        <h1 style={{
          position: 'absolute',
          bottom: 80,
          width: '100%',
          textAlign: 'center',
          color: 'white',
          fontSize: 72,
          fontWeight: 'bold',
          textShadow: '2px 2px 4px rgba(0,0,0,0.7)',
          zIndex: 10,
          userSelect: 'none',
        }}>
          Peaceful Relax Working Music
        </h1>

        {/* Fixed music bar at bottom center */}
        <div
          style={{
            position: 'absolute',
            bottom: 30,
            left: '50%',
            transform: 'translateX(-50%)',
            display: 'flex',
            gap: 4,
            width: NUM_BARS * 15,
            height: 60,
            zIndex: 20,
            alignItems: 'flex-end',
          }}
        >
          {Array.from({ length: NUM_BARS }).map((_, i) => {
            const height = 20 + (1 - Math.abs(Math.sin((frame + i * 10) / 10))) * barMaxHeight;
            return (
              <div
                key={i}
                style={{
                  width: 10,
                  height,
                  borderRadius: 3,
                  background: 'linear-gradient(to top, #00faff, #00ff80)',
                  boxShadow: '0 0 5px #00faff, 0 0 10px #00ffcc',
                  transition: 'height 0.2s ease-in-out',
                }}
              />
            );
          })}
        </div>
      </AbsoluteFill>
    </div>
  );
};
