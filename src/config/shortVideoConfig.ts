// Configuration for Short Video composition
export const shortVideoConfig = {
  // Video settings
  fps: 30,
  transitionDurationInFrames: 15, // 0.5 seconds at 30fps

  // Dimensions (9:16 aspect ratio for vertical video)
  width: 1080,
  height: 1920,

  // Styling
  backgroundColor: '#000000',

  // Overlay settings
  overlays: {
    videoCounter: {
      enabled: true,
      position: { top: 40, right: 40 },
      style: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        padding: '10px 20px',
        borderRadius: 20,
        fontSize: 24,
        fontWeight: 'bold' as const,
      }
    },

    videoTitle: {
      enabled: true,
      position: { bottom: 100, left: 40, right: 40 },
      style: {
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: 'white',
        padding: '20px',
        borderRadius: 10,
        fontSize: 32,
        fontWeight: 'bold' as const,
        textAlign: 'center' as const,
      }
    },

    progressBar: {
      enabled: true,
      position: { bottom: 40, left: 40, right: 40 },
      height: 6,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      barColor: '#ff4444',
      borderRadius: 3,
    },

    watermark: {
      enabled: false,
      text: 'Short Video',
      position: 'top-left' as const,
      style: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        color: 'white',
        padding: '5px 10px',
        borderRadius: 5,
        fontSize: 16,
        opacity: 0.8,
      }
    }
  },

  // Video processing
  videoSettings: {
    objectFit: 'cover' as const,
    enableTransitions: true,
    loopVideos: false, // Set to true to loop the entire sequence
  }
};

export type ShortVideoConfig = typeof shortVideoConfig;
