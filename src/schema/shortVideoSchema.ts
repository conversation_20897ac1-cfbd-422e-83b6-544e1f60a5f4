import { z } from 'zod';
import { videos } from '../video-list';

// Schema cho video title customization
export const shortVideoSchema = z.object({
  // Video titles - mỗi video có thể có title riêng
  videoTitles: z.object(
    videos.reduce((acc, video, index) => {
      // Tạo key cho mỗi video và default value là tên file (không extension)
      const videoKey = `video_${index}`;
      const defaultTitle = video.filename.replace(/\.[^/.]+$/, ''); // Remove extension

      acc[videoKey] = z.string().default(defaultTitle);
      return acc;
    }, {} as Record<string, z.ZodDefault<z.ZodString>>)
  ).default(
    videos.reduce((acc, video, index) => {
      const videoKey = `video_${index}`;
      const defaultTitle = video.filename.replace(/\.[^/.]+$/, '');
      acc[videoKey] = defaultTitle;
      return acc;
    }, {} as Record<string, string>)
  ),

  // Global settings
  showVideoTitles: z.boolean().default(true),
  showVideoCounter: z.boolean().default(true),
  showProgressBar: z.boolean().default(true),
  showLogo: z.boolean().default(true),

  // Styling options
  titleStyle: z.object({
    fontSize: z.number().min(16).max(60).default(32),
    fontWeight: z.enum(['normal', 'bold', 'bolder']).default('bold'),
    color: z.string().default('#ffffff'),
    backgroundColor: z.string().default('rgba(0, 0, 0, 0.7)'),
    borderRadius: z.number().min(0).max(20).default(10),
  }).default({
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 10,
  }),
});

export type ShortVideoProps = z.infer<typeof shortVideoSchema>;

// Helper function to get video titles as array
export const getVideoTitles = (props: ShortVideoProps): string[] => {
  return videos.map((_, index) => {
    const videoKey = `video_${index}`;
    return props.videoTitles[videoKey] || videos[index].filename.replace(/\.[^/.]+$/, '');
  });
};

// Helper function to get default props
export const getDefaultProps = (): ShortVideoProps => {
  return shortVideoSchema.parse({});
};
