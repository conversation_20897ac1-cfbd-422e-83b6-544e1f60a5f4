import { z } from 'zod';
import { videos } from '../video-list';

// Create video titles schema dynamically
const createVideoTitlesSchema = () => {
  if (videos.length === 0) {
    return z.object({}).default({});
  }

  const titleFields: Record<string, z.ZodDefault<z.ZodString>> = {};
  const defaultValues: Record<string, string> = {};

  videos.forEach((video, index) => {
    const videoKey = `video_${index}`;
    const defaultTitle = video.filename.replace(/\.[^/.]+$/, '');

    titleFields[videoKey] = z.string().default(defaultTitle);
    defaultValues[videoKey] = defaultTitle;
  });

  return z.object(titleFields).default(defaultValues);
};

// Schema cho video title customization
export const shortVideoSchema = z.object({
  // Video titles - mỗi video có thể có title riêng
  videoTitles: createVideoTitlesSchema(),

  // Global settings
  showVideoTitles: z.boolean().default(true),
  showVideoCounter: z.boolean().default(true),
  showProgressBar: z.boolean().default(true),
  showLogo: z.boolean().default(true),

  // Styling options
  titleStyle: z.object({
    fontSize: z.number().min(16).max(60).default(32),
    fontWeight: z.enum(['normal', 'bold', 'bolder']).default('bold'),
    color: z.string().default('#ffffff'),
    backgroundColor: z.string().default('rgba(0, 0, 0, 0.7)'),
    borderRadius: z.number().min(0).max(20).default(10),
  }).default({
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 10,
  }),
});

export type ShortVideoProps = z.infer<typeof shortVideoSchema>;

// Helper function to get video titles as array
export const getVideoTitles = (props?: Partial<ShortVideoProps>): string[] => {
  // Handle case when no videos available
  if (!videos || videos.length === 0) {
    return [];
  }

  return videos.map((video, index) => {
    const videoKey = `video_${index}`;
    const defaultTitle = video?.filename?.replace(/\.[^/.]+$/, '') || `Video ${index + 1}`;

    // Safe access to props with multiple fallbacks
    try {
      if (props?.videoTitles && typeof props.videoTitles === 'object' && props.videoTitles[videoKey]) {
        return props.videoTitles[videoKey];
      }
    } catch (error) {
      console.warn(`Error accessing video title for ${videoKey}:`, error);
    }

    return defaultTitle;
  });
};

// Helper function to get default props
export const getDefaultProps = (): ShortVideoProps => {
  return shortVideoSchema.parse({});
};
