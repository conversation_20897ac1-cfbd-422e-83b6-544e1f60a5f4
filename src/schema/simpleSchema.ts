import { z } from 'zod';

// Simple schema without dynamic video titles
export const simpleShortVideoSchema = z.object({
  // Simple video title override
  customTitle: z.string().default('My Short Video'),
  
  // Basic toggles
  showVideoTitles: z.boolean().default(true),
  showVideoCounter: z.boolean().default(true),
  showProgressBar: z.boolean().default(true),
  showLogo: z.boolean().default(true),
  
  // Simple styling
  titleFontSize: z.number().min(16).max(60).default(32),
  titleColor: z.string().default('#ffffff'),
});

export type SimpleShortVideoProps = z.infer<typeof simpleShortVideoSchema>;
