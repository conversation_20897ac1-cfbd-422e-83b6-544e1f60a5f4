import { z } from 'zod';

// Simple schema without dynamic video titles
export const simpleShortVideoSchema = z.object({
  // Simple video title override
  customTitle: z.string().optional().default('My Short Video'),

  // Basic toggles
  showVideoTitles: z.boolean().optional().default(true),
  showVideoCounter: z.boolean().optional().default(true),
  showProgressBar: z.boolean().optional().default(true),
  showLogo: z.boolean().optional().default(true),

  // Simple styling
  titleFontSize: z.number().min(16).max(60).optional().default(32),
  titleColor: z.string().optional().default('#ffffff'),
});

export type SimpleShortVideoProps = z.infer<typeof simpleShortVideoSchema>;
