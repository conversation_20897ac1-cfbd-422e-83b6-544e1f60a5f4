import {
  useCurrentFrame,
  AbsoluteFill,
  useVideoConfig,
} from 'remotion';
import { videos, totalVideoDurationInFrames } from './video-list';
import { VideoTransition } from './components/VideoTransition';
import { VideoCounter, VideoTitle, ProgressBar, Watermark } from './components/VideoOverlays';

const FPS = 30;
const TRANSITION_DURATION = 15; // 0.5 seconds transition

export const ShortVideo = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  // If no videos available, show placeholder
  if (videos.length === 0) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ color: 'white', fontSize: 48, textAlign: 'center' }}>
          <h1>No Videos Found</h1>
          <p style={{ fontSize: 24, marginTop: 20 }}>
            Add video files to public/videos/ folder
          </p>
        </div>
      </AbsoluteFill>
    );
  }

  // Calculate which video should be playing
  let currentVideoIndex = 0;
  let accumulatedFrames = 0;
  let frameInCurrentVideo = frame;

  for (let i = 0; i < videos.length; i++) {
    if (frame < accumulatedFrames + videos[i].durationInFrames) {
      currentVideoIndex = i;
      frameInCurrentVideo = frame - accumulatedFrames;
      break;
    }
    accumulatedFrames += videos[i].durationInFrames;
  }

  // Handle case where frame exceeds total video duration
  if (frame >= totalVideoDurationInFrames) {
    currentVideoIndex = videos.length - 1;
    frameInCurrentVideo = videos[currentVideoIndex].durationInFrames - 1;
  }

  const currentVideo = videos[currentVideoIndex];
  const nextVideo = videos[(currentVideoIndex + 1) % videos.length];

  // Calculate transition opacity
  const isInTransition = frameInCurrentVideo >= currentVideo.durationInFrames - TRANSITION_DURATION;

  const currentOpacity = isInTransition
    ? interpolate(
        frameInCurrentVideo,
        [currentVideo.durationInFrames - TRANSITION_DURATION, currentVideo.durationInFrames],
        [1, 0],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      )
    : 1;

  const nextOpacity = isInTransition
    ? interpolate(
        frameInCurrentVideo,
        [currentVideo.durationInFrames - TRANSITION_DURATION, currentVideo.durationInFrames],
        [0, 1],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      )
    : 0;

  return (
    <AbsoluteFill style={{ backgroundColor: '#000' }}>
      {/* Current video */}
      <Video
        src={staticFile(`videos/${currentVideo.filename}`)}
        startFrom={0}
        endAt={currentVideo.durationInFrames}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: currentOpacity,
        }}
      />

      {/* Next video for transition (only show during transition) */}
      {isInTransition && nextVideo && (
        <Video
          src={staticFile(`videos/${nextVideo.filename}`)}
          startFrom={0}
          endAt={nextVideo.durationInFrames}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            opacity: nextOpacity,
          }}
        />
      )}

      {/* Video counter overlay */}
      <div
        style={{
          position: 'absolute',
          top: 40,
          right: 40,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '10px 20px',
          borderRadius: 20,
          fontSize: 24,
          fontWeight: 'bold',
          zIndex: 10,
        }}
      >
        {currentVideoIndex + 1} / {videos.length}
      </div>

      {/* Video title overlay */}
      <div
        style={{
          position: 'absolute',
          bottom: 100,
          left: 40,
          right: 40,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '20px',
          borderRadius: 10,
          fontSize: 32,
          fontWeight: 'bold',
          textAlign: 'center',
          zIndex: 10,
        }}
      >
        {currentVideo.filename.replace(/\.[^/.]+$/, '')}
      </div>

      {/* Progress bar */}
      <div
        style={{
          position: 'absolute',
          bottom: 40,
          left: 40,
          right: 40,
          height: 6,
          backgroundColor: 'rgba(255, 255, 255, 0.3)',
          borderRadius: 3,
          zIndex: 10,
        }}
      >
        <div
          style={{
            width: `${(frame / durationInFrames) * 100}%`,
            height: '100%',
            backgroundColor: '#ff4444',
            borderRadius: 3,
            transition: 'width 0.1s ease-out',
          }}
        />
      </div>
    </AbsoluteFill>
  );
};
