import React from 'react';
import {
  useCurrentFrame,
  AbsoluteFill,
  useVideoConfig,
  Sequence,
  Video,
  staticFile,
  interpolate,
} from 'remotion';
import { videos, totalVideoDurationInFrames } from './video-list';
import { VideoCounter, VideoTitle, ProgressBar, Watermark, Logo } from './components/VideoOverlays';
import { shortVideoConfig } from './config/shortVideoConfig';

// Simple video component with fade effects
const VideoWithFade: React.FC<{
  video: { filename: string; durationInFrames: number; durationInSeconds: number };
  fadeIn: boolean;
  fadeOut: boolean;
  transitionDuration: number;
}> = ({ video, fadeIn, fadeOut, transitionDuration }) => {
  const frame = useCurrentFrame();

  let opacity = 1;

  // Fade in at the beginning
  if (fadeIn && frame < transitionDuration) {
    opacity = interpolate(
      frame,
      [0, transitionDuration],
      [0, 1],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
  }

  // Fade out at the end
  if (fadeOut && frame >= video.durationInFrames - transitionDuration) {
    opacity = interpolate(
      frame,
      [video.durationInFrames - transitionDuration, video.durationInFrames],
      [1, 0],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
  }

  return (
    <Video
      src={staticFile(`videos/${video.filename}`)}
      style={{
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        opacity,
      }}
    />
  );
};

export const ShortVideo = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  const config = shortVideoConfig;

  // If no videos available, show placeholder
  if (videos.length === 0) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ color: 'white', fontSize: 48, textAlign: 'center' }}>
          <h1>No Videos Found</h1>
          <p style={{ fontSize: 24, marginTop: 20 }}>
            Add video files to public/videos/ folder
          </p>
        </div>
      </AbsoluteFill>
    );
  }

  // Calculate which video should be playing for overlays
  let currentVideoIndex = 0;
  let accumulatedFrames = 0;

  for (let i = 0; i < videos.length; i++) {
    if (frame < accumulatedFrames + videos[i].durationInFrames) {
      currentVideoIndex = i;
      break;
    }
    accumulatedFrames += videos[i].durationInFrames;
  }

  // Handle case where frame exceeds total video duration
  if (frame >= totalVideoDurationInFrames) {
    currentVideoIndex = videos.length - 1;
  }

  const currentVideo = videos[currentVideoIndex];

  return (
    <AbsoluteFill style={{ backgroundColor: config.backgroundColor }}>
      {/* Render each video in its own sequence */}
      {videos.map((video, index) => {
        const isFirstVideo = index === 0;
        const isLastVideo = index === videos.length - 1;
        const transitionDuration = config.transitionDurationInFrames;

        // Calculate start frame for each video
        const startFrame = videos.slice(0, index).reduce((sum, v) => sum + v.durationInFrames, 0);

        return (
          <Sequence
            key={video.filename}
            from={startFrame}
            durationInFrames={video.durationInFrames}
          >
            <VideoWithFade
              video={video}
              fadeIn={!isFirstVideo} // Fade in for all videos except first
              fadeOut={!isLastVideo} // Fade out for all videos except last
              transitionDuration={transitionDuration}
            />
          </Sequence>
        );
      })}

      {/* Overlays */}
      {config.overlays.videoCounter.enabled && (
        <VideoCounter
          current={currentVideoIndex + 1}
          total={videos.length}
          style={{
            ...config.overlays.videoCounter.position,
            ...config.overlays.videoCounter.style
          }}
        />
      )}

      {config.overlays.videoTitle.enabled && (
        <VideoTitle
          title={currentVideo.filename.replace(/\.[^/.]+$/, '')}
          style={{
            ...config.overlays.videoTitle.position,
            ...config.overlays.videoTitle.style
          }}
        />
      )}

      {config.overlays.progressBar.enabled && (
        <ProgressBar
          progress={frame / durationInFrames}
          style={{
            ...config.overlays.progressBar.position,
            height: config.overlays.progressBar.height
          }}
          barColor={config.overlays.progressBar.barColor}
          backgroundColor={config.overlays.progressBar.backgroundColor}
        />
      )}

      {config.overlays.watermark.enabled && (
        <Watermark
          text={config.overlays.watermark.text}
          position={config.overlays.watermark.position}
          style={config.overlays.watermark.style}
        />
      )}
    </AbsoluteFill>
  );
};
