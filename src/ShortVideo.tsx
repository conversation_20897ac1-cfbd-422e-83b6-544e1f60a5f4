import {
  useCurrentFrame,
  AbsoluteFill,
  useVideoConfig,
} from 'remotion';
import { videos, totalVideoDurationInFrames } from './video-list';
import { VideoTransition } from './components/VideoTransition';
import { VideoCounter, VideoTitle, ProgressBar, Watermark } from './components/VideoOverlays';
import { shortVideoConfig } from './config/shortVideoConfig';

export const ShortVideo = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  const config = shortVideoConfig;

  // If no videos available, show placeholder
  if (videos.length === 0) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ color: 'white', fontSize: 48, textAlign: 'center' }}>
          <h1>No Videos Found</h1>
          <p style={{ fontSize: 24, marginTop: 20 }}>
            Add video files to public/videos/ folder
          </p>
        </div>
      </AbsoluteFill>
    );
  }

  // Calculate which video should be playing
  let currentVideoIndex = 0;
  let accumulatedFrames = 0;
  let frameInCurrentVideo = frame;

  for (let i = 0; i < videos.length; i++) {
    if (frame < accumulatedFrames + videos[i].durationInFrames) {
      currentVideoIndex = i;
      frameInCurrentVideo = frame - accumulatedFrames;
      break;
    }
    accumulatedFrames += videos[i].durationInFrames;
  }

  // Handle case where frame exceeds total video duration
  if (frame >= totalVideoDurationInFrames) {
    currentVideoIndex = videos.length - 1;
    frameInCurrentVideo = videos[currentVideoIndex].durationInFrames - 1;
  }

  const currentVideo = videos[currentVideoIndex];
  const nextVideo = videos[(currentVideoIndex + 1) % videos.length];

  return (
    <AbsoluteFill style={{ backgroundColor: config.backgroundColor }}>
      {/* Video with transition */}
      <VideoTransition
        currentVideo={currentVideo}
        nextVideo={nextVideo}
        frameInCurrentVideo={frameInCurrentVideo}
        transitionDuration={config.transitionDurationInFrames}
      />

      {/* Overlays */}
      {config.overlays.videoCounter.enabled && (
        <VideoCounter
          current={currentVideoIndex + 1}
          total={videos.length}
          style={{
            ...config.overlays.videoCounter.position,
            ...config.overlays.videoCounter.style
          }}
        />
      )}

      {config.overlays.videoTitle.enabled && (
        <VideoTitle
          title={currentVideo.filename.replace(/\.[^/.]+$/, '')}
          style={{
            ...config.overlays.videoTitle.position,
            ...config.overlays.videoTitle.style
          }}
        />
      )}

      {config.overlays.progressBar.enabled && (
        <ProgressBar
          progress={frame / durationInFrames}
          style={{
            ...config.overlays.progressBar.position,
            height: config.overlays.progressBar.height
          }}
          barColor={config.overlays.progressBar.barColor}
          backgroundColor={config.overlays.progressBar.backgroundColor}
        />
      )}

      {config.overlays.watermark.enabled && (
        <Watermark
          text={config.overlays.watermark.text}
          position={config.overlays.watermark.position}
          style={config.overlays.watermark.style}
        />
      )}
    </AbsoluteFill>
  );
};
