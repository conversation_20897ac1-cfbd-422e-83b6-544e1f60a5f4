import {
  useCurrentFrame,
  AbsoluteFill,
  useVideoConfig,
  Sequence,
  Video,
  staticFile,
  interpolate,
} from 'remotion';
import { videos, totalVideoDurationInFrames } from './video-list';
import { VideoCounter, VideoTitle, ProgressBar, Watermark } from './components/VideoOverlays';
import { shortVideoConfig } from './config/shortVideoConfig';

// Transition overlay component
const TransitionOverlay: React.FC<{
  nextVideo: { filename: string; durationInFrames: number; durationInSeconds: number };
  transitionDuration: number;
}> = ({ nextVideo, transitionDuration }) => {
  const frame = useCurrentFrame();

  return (
    <AbsoluteFill>
      <Video
        src={staticFile(`videos/${nextVideo.filename}`)}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          opacity: interpolate(
            frame,
            [0, transitionDuration],
            [0, 1],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          ),
        }}
      />
    </AbsoluteFill>
  );
};

export const ShortVideo = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();
  const config = shortVideoConfig;

  // If no videos available, show placeholder
  if (videos.length === 0) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ color: 'white', fontSize: 48, textAlign: 'center' }}>
          <h1>No Videos Found</h1>
          <p style={{ fontSize: 24, marginTop: 20 }}>
            Add video files to public/videos/ folder
          </p>
        </div>
      </AbsoluteFill>
    );
  }

  // Calculate which video should be playing for overlays
  let currentVideoIndex = 0;
  let accumulatedFrames = 0;

  for (let i = 0; i < videos.length; i++) {
    if (frame < accumulatedFrames + videos[i].durationInFrames) {
      currentVideoIndex = i;
      break;
    }
    accumulatedFrames += videos[i].durationInFrames;
  }

  // Handle case where frame exceeds total video duration
  if (frame >= totalVideoDurationInFrames) {
    currentVideoIndex = videos.length - 1;
  }

  const currentVideo = videos[currentVideoIndex];

  return (
    <AbsoluteFill style={{ backgroundColor: config.backgroundColor }}>
      {/* Render each video as a sequence */}
      {videos.map((video, index) => {
        const startFrame = videos.slice(0, index).reduce((sum, v) => sum + v.durationInFrames, 0);
        return (
          <Sequence
            key={video.filename}
            from={startFrame}
            durationInFrames={video.durationInFrames}
          >
            <Video
              src={staticFile(`videos/${video.filename}`)}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          </Sequence>
        );
      })}

      {/* Add transition overlays between videos */}
      {videos.map((video, index) => {
        if (index === videos.length - 1) return null; // No transition after last video

        const startFrame = videos.slice(0, index + 1).reduce((sum, v) => sum + v.durationInFrames, 0) - config.transitionDurationInFrames;
        const nextVideo = videos[index + 1];

        return (
          <Sequence
            key={`transition-${index}`}
            from={startFrame}
            durationInFrames={config.transitionDurationInFrames}
          >
            <TransitionOverlay nextVideo={nextVideo} transitionDuration={config.transitionDurationInFrames} />
          </Sequence>
        );
      })}

      {/* Overlays */}
      {config.overlays.videoCounter.enabled && (
        <VideoCounter
          current={currentVideoIndex + 1}
          total={videos.length}
          style={{
            ...config.overlays.videoCounter.position,
            ...config.overlays.videoCounter.style
          }}
        />
      )}

      {config.overlays.videoTitle.enabled && (
        <VideoTitle
          title={currentVideo.filename.replace(/\.[^/.]+$/, '')}
          style={{
            ...config.overlays.videoTitle.position,
            ...config.overlays.videoTitle.style
          }}
        />
      )}

      {config.overlays.progressBar.enabled && (
        <ProgressBar
          progress={frame / durationInFrames}
          style={{
            ...config.overlays.progressBar.position,
            height: config.overlays.progressBar.height
          }}
          barColor={config.overlays.progressBar.barColor}
          backgroundColor={config.overlays.progressBar.backgroundColor}
        />
      )}

      {config.overlays.watermark.enabled && (
        <Watermark
          text={config.overlays.watermark.text}
          position={config.overlays.watermark.position}
          style={config.overlays.watermark.style}
        />
      )}
    </AbsoluteFill>
  );
};
