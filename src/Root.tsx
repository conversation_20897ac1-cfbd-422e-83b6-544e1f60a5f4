import "./index.css";
import { Composition } from "remotion";
import { MusicVideo } from "./MusicVideo";
import { ShortVideo } from "./ShortVideo";
import { audioDurationInFrames } from './audio-duration';
import { totalVideoDurationInFrames } from './video-list';
import { shortVideoConfig } from './config/shortVideoConfig';

export const RemotionRoot: React.FC = () => {
  return (
    <>
      <Composition
        id="MusicVideo"
        component={MusicVideo}
        durationInFrames={audioDurationInFrames}
        fps={30}
        width={1920}
        height={1080}
      />
      <Composition
        id="ShortVideo"
        component={ShortVideo}
        durationInFrames={totalVideoDurationInFrames || 1800} // Default 60 seconds if no videos
        fps={30}
        width={1080}
        height={1920}
      />
    </>
  );
};
