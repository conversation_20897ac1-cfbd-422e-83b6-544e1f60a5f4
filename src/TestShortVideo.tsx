import React from 'react';
import {
  useCurrentFrame,
  AbsoluteFill,
  useVideoConfig,
  Sequence,
  Video,
  staticFile,
} from 'remotion';
import { videos, totalVideoDurationInFrames } from './video-list';

// Test component without any schema or complex props
export const TestShortVideo: React.FC = () => {
  const frame = useCurrentFrame();
  const { durationInFrames } = useVideoConfig();

  console.log('TestShortVideo - Frame:', frame);
  console.log('TestShortVideo - Videos:', videos.length);

  // If no videos available, show placeholder
  if (!videos || videos.length === 0) {
    return (
      <AbsoluteFill style={{ backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }}>
        <div style={{ color: 'white', fontSize: 48, textAlign: 'center' }}>
          <h1>No Videos Found</h1>
          <p style={{ fontSize: 24, marginTop: 20 }}>
            Add video files to public/videos/ folder
          </p>
        </div>
      </AbsoluteFill>
    );
  }

  // Calculate which video should be playing
  let currentVideoIndex = 0;
  let accumulatedFrames = 0;

  for (let i = 0; i < videos.length; i++) {
    if (frame < accumulatedFrames + videos[i].durationInFrames) {
      currentVideoIndex = i;
      break;
    }
    accumulatedFrames += videos[i].durationInFrames;
  }

  if (frame >= totalVideoDurationInFrames) {
    currentVideoIndex = videos.length - 1;
  }

  const currentVideo = videos[currentVideoIndex];

  return (
    <AbsoluteFill style={{ backgroundColor: '#000' }}>
      {/* Render each video in its own sequence */}
      {videos.map((video, index) => {
        const startFrame = videos.slice(0, index).reduce((sum, v) => sum + v.durationInFrames, 0);

        return (
          <Sequence
            key={video.filename}
            from={startFrame}
            durationInFrames={video.durationInFrames}
          >
            <Video
              src={staticFile(`videos/${video.filename}`)}
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
              }}
            />
          </Sequence>
        );
      })}

      {/* Simple overlay */}
      <div
        style={{
          position: 'absolute',
          top: 40,
          right: 40,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '10px 20px',
          borderRadius: 20,
          fontSize: 24,
          fontWeight: 'bold',
          zIndex: 10,
        }}
      >
        {currentVideoIndex + 1} / {videos.length}
      </div>

      {/* Simple title */}
      <div
        style={{
          position: 'absolute',
          bottom: 100,
          left: 40,
          right: 40,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          color: 'white',
          padding: '20px',
          borderRadius: 10,
          fontSize: 32,
          fontWeight: 'bold',
          textAlign: 'center',
          zIndex: 10,
        }}
      >
        {currentVideo?.filename?.replace(/\.[^/.]+$/, '') || 'Video'}
      </div>
    </AbsoluteFill>
  );
};
