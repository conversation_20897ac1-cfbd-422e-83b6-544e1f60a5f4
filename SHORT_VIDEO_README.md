# Short Video Composition

This project now includes a new **ShortVideo** composition that combines multiple short videos into a single YouTube Shorts-style video (9:16 aspect ratio).

## Features

- **9:16 Aspect Ratio**: Optimized for YouTube Shorts, TikTok, Instagram Reels
- **Automatic Video Sequencing**: Plays multiple videos in sequence
- **Smooth Transitions**: Cross-fade transitions between videos
- **Progress Indicators**: Shows current video number and overall progress
- **Video Titles**: Displays filename as title overlay
- **Responsive Design**: Handles videos of different lengths

## Setup

### 1. Add Video Files

Place your video files in the `public/videos/` directory:

```
public/
  videos/
    video1.mp4
    video2.mov
    video3.mp4
    ...
```

Supported formats: `.mp4`, `.mov`, `.avi`, `.mkv`, `.webm`

### 2. Generate Video List

Run the prepare assets script to scan video files and calculate durations:

```bash
npm run prepare:assets
```

This will:
- Scan `public/videos/` for video files
- Calculate duration of each video
- Generate `src/video-list.ts` with video metadata
- Update the composition duration automatically

### 3. Pre<PERSON> and <PERSON><PERSON>

Start the development server:

```bash
npm run dev
```

In Remotion Studio, you'll see two compositions:
- **MusicVideo**: Original landscape music video (1920x1080)
- **ShortVideo**: New portrait short video (1080x1920)

## Composition Details

### Dimensions
- **Width**: 1080px
- **Height**: 1920px (9:16 aspect ratio)
- **FPS**: 30

### Video Sequencing
- Videos play in alphabetical order by filename
- Each video plays for its full duration
- 0.5-second cross-fade transition between videos
- Total composition duration = sum of all video durations

### Overlays
1. **Video Counter**: Top-right corner shows "X / Y" format
2. **Video Title**: Bottom overlay shows filename (without extension)
3. **Progress Bar**: Bottom progress indicator for entire composition

## Customization

### Transition Duration
Edit `TRANSITION_DURATION` in `src/ShortVideo.tsx`:

```typescript
const TRANSITION_DURATION = 15; // frames (0.5 seconds at 30fps)
```

### Styling
Modify overlay styles in `src/ShortVideo.tsx`:
- Video counter position and styling
- Title overlay appearance
- Progress bar design
- Background color

### Video Order
Videos are processed alphabetically by filename. To control order:
- Prefix filenames with numbers: `01_intro.mp4`, `02_main.mp4`, `03_outro.mp4`
- Or rename files in desired sequence

## File Structure

```
src/
  ShortVideo.tsx          # Main short video component
  video-list.ts           # Auto-generated video metadata
  Root.tsx               # Updated with ShortVideo composition

scripts/
  prepare-assets.ts      # Updated to process videos

public/
  videos/               # Place your video files here
    sample1.mp4
    sample2.mp4
    ...
```

## Rendering

To render the short video:

```bash
npx remotion render ShortVideo out/short-video.mp4
```

Options:
- `--codec h264` for MP4 output
- `--crf 18` for high quality
- `--audio-codec aac` for audio

## Tips

1. **Video Quality**: Use consistent resolution videos for best results
2. **File Sizes**: Keep individual videos under 100MB for smooth preview
3. **Naming**: Use descriptive filenames as they appear as titles
4. **Duration**: Aim for 15-60 seconds total for optimal social media engagement
5. **Content**: Ensure videos work well in portrait orientation

## Troubleshooting

### No Videos Found
- Check that videos are in `public/videos/` directory
- Ensure video files have supported extensions
- Run `npm run prepare:assets` after adding videos

### Video Not Playing
- Verify video format is supported by browser
- Check video file isn't corrupted
- Ensure video path is correct in `video-list.ts`

### Duration Issues
- Re-run `npm run prepare:assets` after changing videos
- Check that video metadata is correctly calculated
- Verify `totalVideoDurationInFrames` matches expected duration
