import fs from 'fs';
import path from 'path';
import { parseFile } from 'music-metadata';
import { getVideoMetadata } from '@remotion/media-utils';

const IMAGE_FOLDER = path.join(__dirname, '../public/images');
const IMAGE_OUTPUT = path.join(__dirname, '../src/image-list.ts');

const VIDEO_FOLDER = path.join(__dirname, '../public/videos');
const VIDEO_OUTPUT = path.join(__dirname, '../src/video-list.ts');

const AUDIO_SOURCE = path.join(__dirname, '../public/music/song.mp3');
const AUDIO_OUTPUT = path.join(__dirname, '../src/audio-duration.ts');

const FPS = 30;

(async () => {
  // Generate image-list.ts
  const imageFiles = fs
    .readdirSync(IMAGE_FOLDER)
    .filter((file) => /\.(jpe?g|png|gif|webp)$/i.test(file));

  const imageExport = `// Auto-generated\nexport const images = ${JSON.stringify(
    imageFiles,
    null,
    2
  )};\n`;

  fs.writeFileSync(IMAGE_OUTPUT, imageExport);
  console.log(`✅ image-list.ts created with ${imageFiles.length} images`);

  // Generate audio-duration.ts
  const metadata = await parseFile(AUDIO_SOURCE);
  const durationSeconds = metadata.format.duration ?? 0;
  const durationFrames = Math.floor(durationSeconds * FPS);
  const audioExport = `// Auto-generated\nexport const audioDurationInFrames = ${durationFrames};\n`;

  fs.writeFileSync(AUDIO_OUTPUT, audioExport);
  console.log(`✅ audio-duration.ts created (${durationFrames} frames = ${durationSeconds.toFixed(2)}s)`);
})();
