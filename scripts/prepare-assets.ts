import fs from 'fs';
import path from 'path';
import { parseFile } from 'music-metadata';
import { parseMedia } from '@remotion/media-parser';

const IMAGE_FOLDER = path.join(__dirname, '../public/images');
const IMAGE_OUTPUT = path.join(__dirname, '../src/image-list.ts');

const VIDEO_FOLDER = path.join(__dirname, '../public/videos');
const VIDEO_OUTPUT = path.join(__dirname, '../src/video-list.ts');

const AUDIO_SOURCE = path.join(__dirname, '../public/music/song.mp3');
const AUDIO_OUTPUT = path.join(__dirname, '../src/audio-duration.ts');

const FPS = 30;

(async () => {
  // Generate image-list.ts
  const imageFiles = fs
    .readdirSync(IMAGE_FOLDER)
    .filter((file) => /\.(jpe?g|png|gif|webp)$/i.test(file));

  const imageExport = `// Auto-generated\nexport const images = ${JSON.stringify(
    imageFiles,
    null,
    2
  )};\n`;

  fs.writeFileSync(IMAGE_OUTPUT, imageExport);
  console.log(`✅ image-list.ts created with ${imageFiles.length} images`);

  // Generate video-list.ts
  if (fs.existsSync(VIDEO_FOLDER)) {
    const videoFiles = fs
      .readdirSync(VIDEO_FOLDER)
      .filter((file) => /\.(mp4|mov|avi|mkv|webm)$/i.test(file));

    // Calculate video durations
    const videoData = [];
    let totalDurationFrames = 0;

    for (const videoFile of videoFiles) {
      try {
        const videoPath = path.join(VIDEO_FOLDER, videoFile);
        const metadata = await getVideoMetadata(videoPath);
        const durationFrames = Math.floor(metadata.durationInSeconds * FPS);

        videoData.push({
          filename: videoFile,
          durationInFrames: durationFrames,
          durationInSeconds: metadata.durationInSeconds
        });

        totalDurationFrames += durationFrames;
      } catch (error) {
        console.warn(`⚠️ Could not get metadata for ${videoFile}:`, error.message);
      }
    }

    const videoExport = `// Auto-generated
export const videos = ${JSON.stringify(
      videoData,
      null,
      2
    )};

export const totalVideoDurationInFrames = ${totalDurationFrames};
`;

    fs.writeFileSync(VIDEO_OUTPUT, videoExport);
    console.log(`✅ video-list.ts created with ${videoData.length} videos (${totalDurationFrames} frames = ${(totalDurationFrames / FPS).toFixed(2)}s)`);
  } else {
    // Create empty video list if folder doesn't exist
    const emptyVideoExport = `// Auto-generated
export const videos: Array<{filename: string; durationInFrames: number; durationInSeconds: number}> = [];

export const totalVideoDurationInFrames = 0;
`;

    fs.writeFileSync(VIDEO_OUTPUT, emptyVideoExport);
    console.log(`✅ video-list.ts created (empty - no videos folder found)`);
  }

  // Generate audio-duration.ts
  const metadata = await parseFile(AUDIO_SOURCE);
  const durationSeconds = metadata.format.duration ?? 0;
  const durationFrames = Math.floor(durationSeconds * FPS);
  const audioExport = `// Auto-generated\nexport const audioDurationInFrames = ${durationFrames};\n`;

  fs.writeFileSync(AUDIO_OUTPUT, audioExport);
  console.log(`✅ audio-duration.ts created (${durationFrames} frames = ${durationSeconds.toFixed(2)}s)`);
})();
