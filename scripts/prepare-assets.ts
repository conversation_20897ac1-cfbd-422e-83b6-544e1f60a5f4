import fs from 'fs';
import path from 'path';
import { getAudioDurationInSeconds } from '@remotion/media-utils';

const IMAGE_FOLDER = path.join(__dirname, '../public/images');
const IMAGE_OUTPUT = path.join(__dirname, '../scripts/image-list.ts');

const AUDIO_SOURCE = path.join(__dirname, '../assets/song.mp3'); // ✅ dùng file local
const AUDIO_OUTPUT = path.join(__dirname, '../scripts/audio-duration.ts');

const FPS = 30;

(async () => {
  // Generate image-list.ts
  const imageFiles = fs
    .readdirSync(IMAGE_FOLDER)
    .filter((file) => /\.(jpe?g|png|gif|webp)$/i.test(file));

  const imageExport = `// Auto-generated\nexport const images = ${JSON.stringify(
    imageFiles,
    null,
    2
  )};\n`;

  fs.writeFileSync(IMAGE_OUTPUT, imageExport);
  console.log(`✅ image-list.ts created with ${imageFiles.length} images`);

  // Generate audio-duration.ts
  const durationSeconds = await getAudioDurationInSeconds(AUDIO_SOURCE);
  const durationFrames = Math.floor(durationSeconds * FPS);
  const audioExport = `// Auto-generated\nexport const audioDurationInFrames = ${durationFrames};\n`;

  fs.writeFileSync(AUDIO_OUTPUT, audioExport);
  console.log(`✅ audio-duration.ts created (${durationFrames} frames = ${durationSeconds.toFixed(2)}s)`);
})();
