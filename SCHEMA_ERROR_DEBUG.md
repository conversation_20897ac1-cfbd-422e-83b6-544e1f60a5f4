# Debug Schema Error - Step by Step

## 🔍 **Debugging Steps**

### **Step 1: Test Simple Schema First**

Tôi đã tạo một **SimpleShortVideo** composition để test schema cơ bản:

```bash
npm run dev
```

**Trong Remotion Studio:**
1. **Chọn "SimpleShortVideo"** composition (thay vì ShortVideo)
2. **<PERSON><PERSON><PERSON> tra Props Panel** có xuất hiện không
3. **Test basic controls:**
   - Custom Title: "My Test Video"
   - Show Video Counter: ✅/❌
   - Title Font Size: 32
   - Title Color: #ffffff

### **Step 2: Check Console Logs**

Mở **Browser DevTools** (F12) và xem Console:

```javascript
// Expected logs:
SimpleShortVideo props: { customTitle: "My Test Video", ... }
Videos available: 3

// Error logs (nếu có):
Error getting video titles: [error details]
```

### **Step 3: Verify Video Data**

<PERSON><PERSON><PERSON> tra video-list.ts có data đúng:

```typescript
// Should show:
videos = [
  { filename: "ace.mp4", durationInFrames: 169, ... },
  { filename: "luffy-dragon.mp4", durationInFrames: 169, ... },
  { filename: "sabo.mp4", durationInFrames: 169, ... }
]
```

## 🛠️ **Troubleshooting**

### **Issue 1: Schema không load**
```bash
# Reinstall dependencies
npm install

# Check zod version
npm list zod
```

### **Issue 2: Props Panel không xuất hiện**
- Restart Remotion Studio
- Clear browser cache
- Check browser console for errors

### **Issue 3: Video titles undefined**
```typescript
// Check trong browser console:
console.log('Videos:', videos);
console.log('Props:', props);
```

## 🔧 **Fallback Solutions**

### **Solution 1: Use SimpleShortVideo**
Nếu ShortVideo vẫn lỗi, sử dụng SimpleShortVideo:
- Chỉ có 1 custom title cho tất cả videos
- Basic controls đơn giản
- Ít phức tạp hơn

### **Solution 2: Disable Schema Temporarily**
```typescript
// Trong Root.tsx, comment out schema
<Composition
  id="ShortVideo"
  component={ShortVideo}
  // schema={shortVideoSchema}  // Comment this line
/>
```

### **Solution 3: Manual Props**
```typescript
// Test với props cố định
const testProps = {
  showVideoTitles: true,
  showVideoCounter: true,
  customTitle: "Test Video"
};
```

## 📋 **Debug Checklist**

### **Environment:**
- ✅ Node.js version: `node --version`
- ✅ NPM packages: `npm list`
- ✅ Zod installed: `npm list zod`

### **Files:**
- ✅ `src/video-list.ts` có data
- ✅ `src/schema/simpleSchema.ts` exists
- ✅ `src/SimpleShortVideo.tsx` exists
- ✅ Browser console không có errors

### **Remotion Studio:**
- ✅ SimpleShortVideo composition xuất hiện
- ✅ Props Panel hiển thị
- ✅ Controls hoạt động
- ✅ Preview updates khi thay đổi props

## 🎯 **Expected Results**

### **SimpleShortVideo should work:**
- ✅ Composition loads without errors
- ✅ Props Panel shows simple controls
- ✅ Video plays with overlays
- ✅ Custom title displays
- ✅ Toggle controls work

### **If SimpleShortVideo works:**
- Schema system hoạt động
- Vấn đề chỉ ở complex dynamic schema
- Có thể fix ShortVideo sau

### **If SimpleShortVideo fails:**
- Vấn đề cơ bản với zod/schema setup
- Cần check environment và dependencies
- Có thể cần reinstall packages

## 🚀 **Next Steps**

1. **Test SimpleShortVideo first**
2. **Report results** - works hay vẫn lỗi?
3. **Check console logs** cho error details
4. **Based on results** → fix approach

**Hãy test SimpleShortVideo composition trước và báo kết quả!** 🔍
